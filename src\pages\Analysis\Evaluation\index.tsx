import * as reviews from '@/services/reviews';
import { EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProCard,
  ProColumns,
  ProTable,
} from '@ant-design/pro-components';
import {
  Button,
  Col,
  DatePicker,
  message,
  Rate,
  Row,
  Space,
  Tag,
  Tooltip,
} from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import RatingDistribution from './components/RatingDistribution';
import ReviewDetailModal from './components/ReviewDetailModal';
import StatisticsCards from './components/StatisticsCards';
import TrendChart from './components/TrendChart';

const { RangePicker } = DatePicker;

// 统计数据接口
interface StatisticsData {
  totalReviews: number;
  todayReviews: number;
  averageRating: number;
  positiveRate: number;
  thisWeekReviews: number;
  thisMonthReviews: number;
}

// 评价列表数据接口
interface ReviewData {
  id: number;
  orderId: number;
  rating: number;
  comment?: string;
  photoURLs?: string[];
  createdAt?: string;
  order?: {
    sn: string;
  };
  customer?: API.Customer;
  employee?: API.Employee;
}

const EvaluationAnalysis: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<StatisticsData>({
    totalReviews: 0,
    todayReviews: 0,
    averageRating: 0,
    positiveRate: 0,
    thisWeekReviews: 0,
    thisMonthReviews: 0,
  });

  // 评价详情相关状态
  const [reviewDetailVisible, setReviewDetailVisible] = useState(false);
  const [currentReview, setCurrentReview] = useState<ReviewData | null>(null);

  // 日期范围状态
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'days'),
    dayjs(),
  ]);

  // 获取统计数据
  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await reviews.statistics();

      if (errCode) {
        message.error(msg || '获取统计数据失败');
        return;
      }

      if (data) {
        setStatistics({
          totalReviews: data.totalReviews,
          todayReviews: data.todayReviews,
          averageRating: data.averageRating,
          positiveRate: data.positiveRate,
          thisWeekReviews: data.thisWeekReviews,
          thisMonthReviews: data.thisMonthReviews,
        });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, []);

  // 查看评价详情
  const handleViewReviewDetail = (record: ReviewData) => {
    setCurrentReview(record);
    setReviewDetailVisible(true);
  };

  // 关闭评价详情
  const handleCloseReviewDetail = () => {
    setReviewDetailVisible(false);
    setCurrentReview(null);
  };

  // 刷新数据
  const handleRefresh = () => {
    actionRef.current?.reload();
    fetchStatistics();
  };

  // 表格列定义
  const columns: ProColumns<ReviewData>[] = [
    {
      title: '评价ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '订单编号',
      dataIndex: ['order', 'sn'],
      key: 'orderSn',
      width: 150,
      copyable: true,
    },
    {
      title: '客户信息',
      dataIndex: 'customer',
      key: 'customer',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <span>{record.customer?.nickname || '未知客户'}</span>
          <span style={{ fontSize: '12px', color: '#999' }}>
            {record.customer?.phone}
          </span>
        </Space>
      ),
    },
    {
      title: '服务员工',
      dataIndex: 'employee',
      key: 'employee',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <span>{record.employee?.name || '未知员工'}</span>
          <span style={{ fontSize: '12px', color: '#999' }}>
            等级: {record.employee?.level || 0}
          </span>
        </Space>
      ),
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      align: 'center',
      valueEnum: {
        5: '5星',
        4: '4星',
        3: '3星',
        2: '2星',
        1: '1星',
      },
      render: (_, record) => (
        <Space direction="vertical" size={0} style={{ textAlign: 'center' }}>
          <Rate disabled value={record.rating} style={{ fontSize: '14px' }} />
          <span style={{ fontSize: '12px', color: '#666' }}>
            {record.rating} 分
          </span>
        </Space>
      ),
    },
    {
      title: '评价内容',
      dataIndex: 'comment',
      key: 'comment',
      width: 200,
      hideInSearch: true,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>{text || '无文字评价'}</span>
        </Tooltip>
      ),
    },
    {
      title: '是否有图片',
      dataIndex: 'photoURLs',
      key: 'hasPhotos',
      width: 100,
      align: 'center',
      hideInSearch: true,
      render: (text) => (
        <Tag color={text ? 'green' : 'default'}>
          {text ? '有图片' : '无图片'}
        </Tag>
      ),
    },
    {
      title: '评价时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewReviewDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <PageContainer
      title="评价数据分析"
      subTitle="订单评价统计与分析"
      extra={[
        <RangePicker
          key="dateRange"
          value={dateRange}
          onChange={(dates) => {
            if (dates) {
              setDateRange(dates as [Dayjs, Dayjs]);
            }
          }}
          style={{ marginRight: 16 }}
        />,
        <Button
          key="refresh"
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
        >
          刷新数据
        </Button>,
      ]}
    >
      {/* 统计卡片区域 */}
      <StatisticsCards statistics={statistics} loading={loading} />

      {/* 图表分析区域 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} lg={12}>
          <TrendChart dateRange={dateRange} />
        </Col>
        <Col xs={24} lg={12}>
          <RatingDistribution />
        </Col>
      </Row>

      {/* 数据表格区域 */}
      <ProCard>
        <ProTable<ReviewData>
          actionRef={actionRef}
          rowKey="id"
          headerTitle="评价记录详情"
          columns={columns}
          search={{
            labelWidth: 'auto',
          }}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
          }}
          scroll={{ x: 1200 }}
          request={async (params, sort, filter) => {
            try {
              const { errCode, msg, data } = await reviews.index({
                ...params,
                ...sort,
                filter,
              });

              if (errCode) {
                message.error(msg || '列表查询失败');
                return {
                  data: [],
                  total: 0,
                  success: false,
                };
              }

              return {
                data: data?.list || [],
                total: data?.total || 0,
                success: true,
              };
            } catch (error) {
              console.error('获取评价列表失败:', error);
              message.error('获取评价列表失败');
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          }}
          toolBarRender={() => [
            <Button
              key="refresh"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>,
          ]}
        />
      </ProCard>

      {/* 评价详情弹窗 */}
      <ReviewDetailModal
        visible={reviewDetailVisible}
        review={currentReview}
        onClose={handleCloseReviewDetail}
      />
    </PageContainer>
  );
};

export default EvaluationAnalysis;

/*
 * @Description: 投诉建议管理
 */
import { request } from '@umijs/max';

/** 查询投诉建议列表  GET /complaints */
export async function index(params: {
  current?: number;
  pageSize?: number;
  category?: API.ComplaintCategory;
  subCategory?: API.ComplaintSubCategory;
  status?: API.ComplaintStatus;
  keyword?: string;
  startDate?: string;
  endDate?: string;
  customerId?: number;
  orderId?: number;
  employeeId?: number;
  handlerId?: number;
}) {
  return request<API.ResType<{ total?: number; list?: API.Complaint[] }>>(
    '/complaints',
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询单个投诉建议  GET /complaints/:id */
export async function show(id: number) {
  return request<API.ResType<API.Complaint>>(`/complaints/${id}`, {
    method: 'GET',
  });
}

/** 创建投诉建议  POST /complaints */
export async function create(
  body: Omit<
    API.Complaint,
    | 'id'
    | 'createdAt'
    | 'updatedAt'
    | 'handlerId'
    | 'handledAt'
    | 'customer'
    | 'order'
    | 'employee'
    | 'handler'
  >,
) {
  return request<API.ResType<API.Complaint>>('/complaints', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 更新投诉建议  PUT /complaints/:id */
export async function update(
  id: number,
  body: Partial<
    Omit<
      API.Complaint,
      | 'id'
      | 'createdAt'
      | 'updatedAt'
      | 'customer'
      | 'order'
      | 'employee'
      | 'handler'
    >
  >,
) {
  return request<API.ResType<API.Complaint>>(`/complaints/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除投诉建议  DELETE /complaints/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/complaints/${id}`, {
    method: 'DELETE',
  });
}

/** 处理投诉建议  PUT /complaints/:id/handle */
export async function handle(
  id: number,
  body: {
    status: API.ComplaintStatus;
    result?: string;
    handlerId: number;
  },
) {
  return request<API.ResType<API.Complaint>>(`/complaints/${id}/handle`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 获取统计信息  GET /complaints/statistics */
export async function statistics(params?: {
  startDate?: string;
  endDate?: string;
  category?: API.ComplaintCategory;
  subCategory?: API.ComplaintSubCategory;
}) {
  return request<API.ResType<API.ComplaintStatistics>>(
    '/complaints/statistics',
    {
      method: 'GET',
      params,
    },
  );
}

/** 根据订单获取投诉建议  GET /complaints/order/:orderId */
export async function getByOrder(orderId: number) {
  return request<API.ResType<{ list?: API.Complaint[] }>>(
    `/complaints/order/${orderId}`,
    {
      method: 'GET',
    },
  );
}

/** 根据员工获取投诉建议  GET /complaints/employee/:employeeId */
export async function getByEmployee(employeeId: number) {
  return request<API.ResType<{ list?: API.Complaint[] }>>(
    `/complaints/employee/${employeeId}`,
    {
      method: 'GET',
    },
  );
}

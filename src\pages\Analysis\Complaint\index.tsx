import * as complaints from '@/services/complaints';
import { ReloadOutlined } from '@ant-design/icons';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { Button, Col, DatePicker, message, Row, Tabs } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import CustomerStats from './components/CustomerStats';
import EmployeeStats from './components/EmployeeStats';
import HotIssuesAnalysis from './components/HotIssuesAnalysis';
import ProcessingEfficiency from './components/ProcessingEfficiency';
import SatisfactionStats from './components/SatisfactionStats';
import StatisticsCards from './components/StatisticsCards';
import StatusDistribution from './components/StatusDistribution';
import TimeDistribution from './components/TimeDistribution';
import TrendChart from './components/TrendChart';

const { RangePicker } = DatePicker;

const ComplaintAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState<API.ComplaintOverviewStats>({
    complaintStats: { total: 0, today: 0, week: 0, month: 0, processedCount: 0, processRate: 0 },
    statusStats: [],
    categoryStats: [],
    subCategoryStats: [],
  });

  // 日期范围状态
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'days'),
    dayjs(),
  ]);

  // 获取概览统计数据
  const fetchOverviewData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await complaints.overview({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取概览数据失败');
        return;
      }

      if (data) {
        setOverviewData(data);
      }
    } catch (error) {
      console.error('获取概览数据失败:', error);
      message.error('获取概览数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
  }, [dateRange]);

  // 刷新数据
  const handleRefresh = () => {
    fetchOverviewData();
  };

  // Tab页签配置
  const tabItems = [
    {
      key: 'overview',
      label: '数据概览',
      children: (
        <div>
          {/* 统计卡片区域 */}
          <StatisticsCards data={overviewData} loading={loading} />

          {/* 图表分析区域 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24} lg={12}>
              <TrendChart dateRange={dateRange} />
            </Col>
            <Col xs={24} lg={12}>
              <StatusDistribution dateRange={dateRange} />
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24} lg={12}>
              <ProcessingEfficiency dateRange={dateRange} />
            </Col>
            <Col xs={24} lg={12}>
              <HotIssuesAnalysis dateRange={dateRange} />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'customer',
      label: '客户统计',
      children: <CustomerStats dateRange={dateRange} />,
    },
    {
      key: 'employee',
      label: '员工统计',
      children: <EmployeeStats dateRange={dateRange} />,
    },
    {
      key: 'analysis',
      label: '深度分析',
      children: (
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <TimeDistribution dateRange={dateRange} />
          </Col>
          <Col xs={24} lg={12}>
            <SatisfactionStats dateRange={dateRange} />
          </Col>
        </Row>
      ),
    },
  ];

  return (
    <PageContainer
      title="投诉建议数据分析"
      subTitle="投诉建议统计与业务分析"
      extra={[
        <RangePicker
          key="dateRange"
          value={dateRange}
          onChange={(dates) => {
            if (dates) {
              setDateRange(dates as [Dayjs, Dayjs]);
            }
          }}
          style={{ marginRight: 16 }}
        />,
        <Button
          key="refresh"
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
        >
          刷新数据
        </Button>,
      ]}
    >
      <ProCard>
        <Tabs items={tabItems} />
      </ProCard>
    </PageContainer>
  );
};

export default ComplaintAnalysis;

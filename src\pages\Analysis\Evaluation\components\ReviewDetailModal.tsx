import { Descriptions, Image, Modal, Rate, Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

const { Paragraph } = Typography;

interface ReviewData {
  id: number;
  orderId: number;
  rating: number;
  comment?: string;
  photoURLs?: string[];
  createdAt?: string;
  order?: {
    sn: string;
    employee: API.Employee;
  };
  customer?: API.Customer;
}

interface ReviewDetailModalProps {
  visible: boolean;
  review: ReviewData | null;
  onClose: () => void;
}

const ReviewDetailModal: React.FC<ReviewDetailModalProps> = ({
  visible,
  review,
  onClose,
}) => {
  if (!review) return null;

  // 解析图片URLs
  const photoUrls = review.photoURLs || [];

  return (
    <Modal
      title="评价详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="评价ID" span={1}>
          {review.id}
        </Descriptions.Item>
        <Descriptions.Item label="订单编号" span={1}>
          {review.order?.sn || '未知订单'}
        </Descriptions.Item>

        <Descriptions.Item label="客户姓名" span={1}>
          {review.customer?.nickname || '未知客户'}
        </Descriptions.Item>
        <Descriptions.Item label="客户电话" span={1}>
          {review.customer?.phone || '未知电话'}
        </Descriptions.Item>

        <Descriptions.Item label="服务员工" span={1}>
          {review.order?.employee?.name || '未知员工'}
        </Descriptions.Item>
        <Descriptions.Item label="员工等级" span={1}>
          <Tag color="blue">等级 {review.order?.employee?.level || 0}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="评分" span={1}>
          <Space>
            <Rate disabled value={review.rating} />
            <span>{review.rating} 分</span>
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="评价时间" span={1}>
          {review.createdAt
            ? dayjs(review.createdAt).format('YYYY-MM-DD HH:mm:ss')
            : '未知时间'}
        </Descriptions.Item>

        <Descriptions.Item label="评价内容" span={2}>
          <Paragraph>{review.comment || '无文字评价'}</Paragraph>
        </Descriptions.Item>

        {photoUrls.length > 0 && (
          <Descriptions.Item label="评价图片" span={2}>
            <Space wrap>
              {photoUrls.map((url, index) => (
                <Image
                  key={index}
                  width={100}
                  height={100}
                  src={url.trim()}
                  style={{ objectFit: 'cover', borderRadius: '4px' }}
                  placeholder={
                    <div
                      style={{
                        width: 100,
                        height: 100,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                      }}
                    >
                      加载中...
                    </div>
                  }
                />
              ))}
            </Space>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};

export default ReviewDetailModal;
